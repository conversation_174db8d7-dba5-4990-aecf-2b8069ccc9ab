networks:
  app-network:
    name: app-network
  shared-network:
    name: shared-network

volumes:
  supabase-config:
  clamav-data:

x-agpt-services:
  &agpt-services
  networks:
    - app-network
    - shared-network

x-supabase-services:
  &supabase-services
  networks:
    - app-network
    - shared-network

services:
  # AGPT services using hub.saitron.net images
  migrate:
    <<: *agpt-services
    image: hub.saitron.net/autogpt/migrate:latest
    command: ["sh", "-c", "poetry run prisma migrate deploy"]
    depends_on:
      db:
        condition: service_healthy
    environment:
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?connect_timeout=60&schema=platform
      - DIRECT_URL=postgresql://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?connect_timeout=60&schema=platform
    restart: on-failure
    healthcheck:
      test: ["CMD", "poetry", "run", "prisma", "migrate", "status"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    <<: *agpt-services
    image: redis:latest
    command: redis-server --requirepass password
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  rabbitmq:
    <<: *agpt-services
    image: rabbitmq:management
    container_name: rabbitmq
    healthcheck:
      test: rabbitmq-diagnostics -q ping
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 10s
    environment:
      - RABBITMQ_DEFAULT_USER=rabbitmq_user_default
      - RABBITMQ_DEFAULT_PASS=k0VMxyIJF9S35f3x2uaw5IWAl6Y536O7
    ports:
      - "5672:5672"
      - "15672:15672"

  rest_server:
    <<: *agpt-services
    image: hub.saitron.net/autogpt/rest_server:latest
    command: ["python", "-m", "backend.rest"]
    depends_on:
      redis:
        condition: service_healthy
      db:
        condition: service_healthy
      migrate:
        condition: service_completed_successfully
      rabbitmq:
        condition: service_healthy
    environment:
      - SUPABASE_URL=http://kong:8000
      - SUPABASE_JWT_SECRET=${JWT_SECRET}
      - SUPABASE_SERVICE_ROLE_KEY=${SERVICE_ROLE_KEY}
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?connect_timeout=60&schema=platform
      - DIRECT_URL=postgresql://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?connect_timeout=60&schema=platform
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_DEFAULT_USER=rabbitmq_user_default
      - RABBITMQ_DEFAULT_PASS=k0VMxyIJF9S35f3x2uaw5IWAl6Y536O7
      - REDIS_PASSWORD=password
      - ENABLE_AUTH=true
      - PYRO_HOST=0.0.0.0
      - SCHEDULER_HOST=scheduler_server
      - EXECUTIONMANAGER_HOST=executor
      - NOTIFICATIONMANAGER_HOST=notification_server
      - CLAMAV_SERVICE_HOST=clamav
      - NEXT_PUBLIC_FRONTEND_BASE_URL=http://localhost:3000
      - BACKEND_CORS_ALLOW_ORIGINS=["http://localhost:3000"]
      - ENCRYPTION_KEY=dvziYgz0KSK8FENhju0ZYi8-fRTfAdlz6YLhdB_jhNw=
      - UNSUBSCRIBE_SECRET_KEY=HlP8ivStJjmbf6NKi78m_3FnOogut0t5ckzjsIqeaio=
    ports:
      - "8006:8006"

  executor:
    <<: *agpt-services
    image: hub.saitron.net/autogpt/executor:latest
    command: ["python", "-m", "backend.exec"]
    depends_on:
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      db:
        condition: service_healthy
      migrate:
        condition: service_completed_successfully
      database_manager:
        condition: service_started
    environment:
      - DATABASEMANAGER_HOST=database_manager
      - SUPABASE_URL=http://kong:8000
      - SUPABASE_JWT_SECRET=${JWT_SECRET}
      - SUPABASE_SERVICE_ROLE_KEY=${SERVICE_ROLE_KEY}
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?connect_timeout=60&schema=platform
      - DIRECT_URL=postgresql://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?connect_timeout=60&schema=platform
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=password
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_DEFAULT_USER=rabbitmq_user_default
      - RABBITMQ_DEFAULT_PASS=k0VMxyIJF9S35f3x2uaw5IWAl6Y536O7
      - ENABLE_AUTH=true
      - PYRO_HOST=0.0.0.0
      - AGENTSERVER_HOST=rest_server
      - NOTIFICATIONMANAGER_HOST=notification_server
      - CLAMAV_SERVICE_HOST=clamav
      - ENCRYPTION_KEY=dvziYgz0KSK8FENhju0ZYi8-fRTfAdlz6YLhdB_jhNw=
    ports:
      - "8002:8002"

  websocket_server:
    <<: *agpt-services
    image: hub.saitron.net/autogpt/websocket_server:latest
    command: ["python", "-m", "backend.ws"]
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      migrate:
        condition: service_completed_successfully
      database_manager:
        condition: service_started
    environment:
      - DATABASEMANAGER_HOST=database_manager
      - SUPABASE_JWT_SECRET=${JWT_SECRET}
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?connect_timeout=60&schema=platform
      - DIRECT_URL=postgresql://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?connect_timeout=60&schema=platform
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=password
      - ENABLE_AUTH=true
      - PYRO_HOST=0.0.0.0
      - BACKEND_CORS_ALLOW_ORIGINS=["http://localhost:3000"]
    ports:
      - "8001:8001"

  database_manager:
    <<: *agpt-services
    image: hub.saitron.net/autogpt/database_manager:latest
    command: ["python", "-m", "backend.db"]
    depends_on:
      db:
        condition: service_healthy
      migrate:
        condition: service_completed_successfully
    environment:
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?connect_timeout=60&schema=platform
      - DIRECT_URL=postgresql://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?connect_timeout=60&schema=platform
      - PYRO_HOST=0.0.0.0
      - ENCRYPTION_KEY=dvziYgz0KSK8FENhju0ZYi8-fRTfAdlz6YLhdB_jhNw=
    ports:
      - "8005:8005"

  scheduler_server:
    <<: *agpt-services
    image: hub.saitron.net/autogpt/scheduler_server:latest
    command: ["python", "-m", "backend.scheduler"]
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      migrate:
        condition: service_completed_successfully
      database_manager:
        condition: service_started
    environment:
      - DATABASEMANAGER_HOST=database_manager
      - NOTIFICATIONMANAGER_HOST=notification_server
      - SUPABASE_JWT_SECRET=${JWT_SECRET}
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?connect_timeout=60&schema=platform
      - DIRECT_URL=postgresql://postgres:${POSTGRES_PASSWORD}@db:5432/postgres?connect_timeout=60&schema=platform
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=password
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_DEFAULT_USER=rabbitmq_user_default
      - RABBITMQ_DEFAULT_PASS=k0VMxyIJF9S35f3x2uaw5IWAl6Y536O7
      - ENABLE_AUTH=true
      - PYRO_HOST=0.0.0.0
      - BACKEND_CORS_ALLOW_ORIGINS=["http://localhost:3000"]
    ports:
      - "8003:8003"

  notification_server:
    <<: *agpt-services
    image: hub.saitron.net/autogpt/notification_server:latest
    command: ["python", "-m", "backend.notification"]
    depends_on:
      db:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      migrate:
        condition: service_completed_successfully
      database_manager:
        condition: service_started
    environment:
      - DATABASEMANAGER_HOST=database_manager
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=password
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_DEFAULT_USER=rabbitmq_user_default
      - RABBITMQ_DEFAULT_PASS=k0VMxyIJF9S35f3x2uaw5IWAl6Y536O7
      - ENABLE_AUTH=true
      - PYRO_HOST=0.0.0.0
      - BACKEND_CORS_ALLOW_ORIGINS=["http://localhost:3000"]
    ports:
      - "8007:8007"

  clamav:
    <<: *agpt-services
    image: clamav/clamav-debian:latest
    ports:
      - "3310:3310"
    volumes:
      - clamav-data:/var/lib/clamav
    environment:
      - CLAMAV_NO_FRESHCLAMD=false
      - CLAMD_CONF_StreamMaxLength=50M
      - CLAMD_CONF_MaxFileSize=100M
      - CLAMD_CONF_MaxScanSize=100M
      - CLAMD_CONF_MaxThreads=12
      - CLAMD_CONF_ReadTimeout=300
    healthcheck:
      test: ["CMD-SHELL", "clamdscan --version || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Supabase services
  studio:
    <<: *supabase-services
    extends:
      file: ./db/docker/docker-compose.yml
      service: studio

  kong:
    <<: *supabase-services
    extends:
      file: ./db/docker/docker-compose.yml
      service: kong

  auth:
    <<: *supabase-services
    extends:
      file: ./db/docker/docker-compose.yml
      service: auth
    environment:
      GOTRUE_MAILER_AUTOCONFIRM: true

  rest:
    <<: *supabase-services
    extends:
      file: ./db/docker/docker-compose.yml
      service: rest

  realtime:
    <<: *supabase-services
    extends:
      file: ./db/docker/docker-compose.yml
      service: realtime

  storage:
    <<: *supabase-services
    extends:
      file: ./db/docker/docker-compose.yml
      service: storage

  imgproxy:
    <<: *supabase-services
    extends:
      file: ./db/docker/docker-compose.yml
      service: imgproxy

  meta:
    <<: *supabase-services
    extends:
      file: ./db/docker/docker-compose.yml
      service: meta

  functions:
    <<: *supabase-services
    extends:
      file: ./db/docker/docker-compose.yml
      service: functions

  analytics:
    <<: *supabase-services
    extends:
      file: ./db/docker/docker-compose.yml
      service: analytics

  db:
    <<: *supabase-services
    extends:
      file: ./db/docker/docker-compose.yml
      service: db
    ports:
      - ${POSTGRES_PORT}:5432

  vector:
    <<: *supabase-services
    extends:
      file: ./db/docker/docker-compose.yml
      service: vector

  deps:
    <<: *supabase-services
    profiles:
      - local
    image: busybox
    command: /bin/true
    depends_on:
      - studio
      - kong
      - auth
      - meta
      - analytics
      - db
      - vector
      - redis
      - rabbitmq
      - clamav
