# AutoGPT 平台生产环境部署

本文档说明如何使用 hub.saitron.net 上的镜像部署 AutoGPT 平台。

## 文件说明

- `docker-compose.production.yml` - 使用 hub.saitron.net 镜像的生产环境配置
- `docker-compose.yml` - 原始开发环境配置（使用本地构建）
- `.env` - 环境变量配置文件

## 使用的镜像

所有 AutoGPT 服务都使用来自 `hub.saitron.net/autogpt/` 的镜像：

- **migrate**: `hub.saitron.net/autogpt/migrate:latest`
- **rest_server**: `hub.saitron.net/autogpt/rest_server:latest`
- **executor**: `hub.saitron.net/autogpt/executor:latest`
- **websocket_server**: `hub.saitron.net/autogpt/websocket_server:latest`
- **database_manager**: `hub.saitron.net/autogpt/database_manager:latest`
- **scheduler_server**: `hub.saitron.net/autogpt/scheduler_server:latest`
- **notification_server**: `hub.saitron.net/autogpt/notification_server:latest`

## 部署步骤

1. 确保已安装 Docker 和 Docker Compose
2. 确保 `.env` 文件存在并配置正确
3. 登录到 Harbor 仓库：
   ```bash
   docker login hub.saitron.net
   ```

4. 启动生产环境：
   ```bash
   docker compose -f docker-compose.production.yml up -d
   ```

5. 查看服务状态：
   ```bash
   docker compose -f docker-compose.production.yml ps
   ```

6. 查看日志：
   ```bash
   docker compose -f docker-compose.production.yml logs -f [service_name]
   ```

## 服务端口

- **REST API**: 8006
- **WebSocket**: 8001
- **Executor**: 8002
- **Scheduler**: 8003
- **Database Manager**: 8005
- **Notification**: 8007
- **Redis**: 6379
- **RabbitMQ**: 5672 (管理界面: 15672)
- **ClamAV**: 3310
- **Supabase Kong**: 8000
- **Supabase Analytics**: 4000
- **PostgreSQL**: 5432

## 停止服务

```bash
docker compose -f docker-compose.production.yml down
```

## 更新镜像

```bash
docker compose -f docker-compose.production.yml pull
docker compose -f docker-compose.production.yml up -d
```

## 注意事项

- 确保所有必要的环境变量在 `.env` 文件中正确设置
- 生产环境建议修改默认密码和密钥
- 定期备份数据库和重要数据
- 监控服务健康状态
